<template>
  <n-drawer v-model:show="visible" width="1100px" :auto-focus="false" :on-after-leave="closeDrawer">
    <n-drawer-content :native-scrollbar="false">
      <template #header>
        <div class="header-container">
          <div class="header-info">
            <n-avatar :size="48" :src="localDetail?.clueInfoVo?.headImg || DefaultAvatar" />
            <div class="header-info-text">
              <div class="name">{{ localDetail?.clueInfoVo?.name || '--' }}</div>
              <div class="time">
                <span>创建时间：{{ localDetail?.clueInfoVo?.createTime || '--' }}</span>
                <span style="margin-left: 30px"
                  >分发时间：{{ localDetail?.clueInfoVo?.triageTime || '--' }}</span
                >
              </div>
            </div>
          </div>
          <n-button text @click="visible = false">
            <template #icon>
              <n-icon :size="40"><Close /></n-icon>
            </template>
          </n-button>
        </div>
      </template>
      <div class="drawer-body" v-loading="isLoading">
        <div class="drawer-body-left">
          <div class="left-content">
            <template v-if="localDetail">
              <InfoSection
                v-if="visibleFieldsByGroup['客户基本信息']?.length > 0"
                title="客户基本信息"
                :fields="visibleFieldsByGroup['客户基本信息']"
                :detail="localDetail"
                :clue-id="clueId"
              />
              <InfoSection
                v-if="visibleFieldsByGroup['车辆信息']?.length > 0"
                title="车辆信息"
                :fields="visibleFieldsByGroup['车辆信息']"
                :detail="localDetail"
                :clue-id="clueId"
              />
            </template>
          </div>

          <div class="left-footer">
            <n-button type="primary" block @click="showFieldControlModal = true">
              字段设置
            </n-button>
          </div>
        </div>
        <div class="drawer-body-right">
          <div class="right-content">
            <n-tabs
              type="line"
              animated
              v-model:value="tabValue"
              @before-leave="handleTabBeforeChange"
            >
              <n-tab-pane name="followUp" tab="跟进记录">
                <!-- 意向信息 -->
                <IntentionDisplay
                  v-if="localDetail?.clueInfoVo"
                  :clue-id="clueId"
                  v-model:intention="localDetail.clueInfoVo.intent"
                  v-model:tags="localDetail.clueInfoVo.tagList"
                  @update-success="handleRefresh"
                />

                <!-- 跟进信息列表 -->
                <FollowUpListTable v-if="localDetail" :data="localDetail" />
                <!--获取车300估价-->
                <VehicleAppraisal
                  :clueId="clueId"
                  @submit-success="handleRefresh"
                  v-if="localDetail"
                  :data="localDetail"
                />
                <!-- 证照信息 -->
                <CredentialsDisplay
                  v-if="
                    localDetail?.loanApplicationMaterials?.certificateInfo &&
                    localDetail?.loanApplicationMaterials?.otherDocuments
                  "
                  :certificate-info="localDetail.loanApplicationMaterials.certificateInfo || {}"
                  :other-documents="localDetail.loanApplicationMaterials.otherDocuments || {}"
                />

                <!-- 跟进详情 -->
                <FollowUpDetails
                  v-if="localDetail"
                  :detail="localDetail"
                  @start-follow-up="addFollowUpDrawerRef?.openDrawer(clueId)"
                />
              </n-tab-pane>
              <!-- <n-tab-pane name="salesOpportunities" tab="销售机会" class="flex flex-col">
                <SalesOpportunityTable :data="localDetail?.salesOpportunities || []" />
              </n-tab-pane> -->
              <n-tab-pane name="loanOrders" tab="贷款订单" class="flex flex-col">
                <LoanOrderTable
                  :data="localDetail?.loanOrderRecords || []"
                  :clue-info-vo="localDetail?.clueInfoVo || {}"
                  :managementCodes="localDetail?.managementCodes || []"
                  @update-success="handleRefresh"
                />
              </n-tab-pane>
              <n-tab-pane name="loanRecord" tab="签约记录" class="flex flex-col">
                <LoanRecord
                  :clueId="clueId"
                  :clue-info-vo="localDetail?.clueInfoVo || {}"
                  @update-success="handleRefresh"
                />
              </n-tab-pane>
              <n-tab-pane
                v-permission="{ action: `detail_update` }"
                name="customsInfoForm"
                tab="客户资料"
                class="flex flex-col"
                :disabled="isUpdateCustomerDisabled"
                :localDetail="localDetail"
              >
                <div>
                  <div class="flex items-center gap-5 mb-[10px]">
                    <n-button
                      v-for="item in submissionCapitalOptions"
                      :key="item.value"
                      :type="customsValue === item.value ? 'primary' : 'default'"
                      size="small"
                      @click="customsValue = item.value as customsValueType"
                      >{{ item.label }}</n-button
                    >
                  </div>
                  <ClientInfoForm
                    class="client-info-form-p"
                    @submit-success="handleRefresh"
                    :showForm="customsValue"
                    :clueId="clueId"
                    :localDetail="localDetail"
                  />
                </div>
              </n-tab-pane>
            </n-tabs>
          </div>
          <div class="right-footer">
            <n-button
              v-permission="{ action: `detail_add` }"
              type="primary"
              ghost
              @click="addFollowUpDrawerRef?.openDrawer(clueId)"
              >添加跟进记录</n-button
            >
            <n-button
              v-permission="{ action: `detail_mark` }"
              type="primary"
              ghost
              @click="handleMarkStatus"
              >标记状态</n-button
            >
            <!-- <n-button
              v-permission="{ action: `detail_update` }"
              type="primary"
              :disabled="isUpdateCustomerDisabled"
              ghost
              @click="updateCustomerModalRef?.openModal(clueId)"
              >补充资料</n-button
            > -->
            <n-button
              v-callPermission
              type="primary"
              ghost
              @click="submitFormDialog({ clubId: clueId })"
              >工作机联系</n-button
            >
          </div>
        </div>
      </div>
    </n-drawer-content>

    <!-- 添加跟进记录 -->
    <AddFollowUpDrawer
      ref="addFollowUpDrawerRef"
      :clue-type="clueType"
      :communication-status="localDetail?.clueInfoVo?.communicationStatus"
      @submit-success="handleRefresh"
    />

    <!-- 字段显示设置 -->
    <FieldControlModal
      v-model:show="showFieldControlModal"
      :storage-key="CUSTOMER_DETAIL_FIELDS_KEY"
      :initial-configs="initialFieldConfigs"
    />

    <!-- 标记状态 -->
    <MarkStatusModal
      v-model:show="showMarkStatusModal"
      :clue-id="clueId"
      :clueInfoVo="localDetail?.clueInfoVo"
      @submit-success="handleRefresh"
    />

    <!-- 补充资料 -->
    <UpdateCustomerModal ref="updateCustomerModalRef" @success="handleRefresh" />
  </n-drawer>
</template>

<script lang="ts" setup>
  import { ref, watch, useTemplateRef, computed } from 'vue';
  import { NAvatar, NButton, NIcon, NTabs, NTabPane } from 'naive-ui';
  import { Close } from '@vicons/ionicons5';
  import DefaultAvatar from '@/assets/images/default-avatar.png';
  import IntentionDisplay from './components/IntentionDisplay.vue';
  import FollowUpListTable from './components/FollowUpListTable.vue';
  import FollowUpDetails from './components/FollowUpDetails.vue';
  import SalesOpportunityTable from './components/SalesOpportunityTable.vue';
  import LoanOrderTable from './components/LoanOrderTable.vue';
  import AddFollowUpDrawer from './components/AddFollowUpDrawer.vue';
  import FieldControlModal from '@/components/FieldControl/FieldControlModal.vue';
  import MarkStatusModal from './components/MarkStatusModal.vue';
  import CredentialsDisplay from './components/CredentialsDisplay.vue';
  import InfoSection from './components/InfoSection.vue';
  import UpdateCustomerModal from '@/views/dashboard/workplace/components/UpdateCustomerModal/UpdateCustomerModal.vue';
  import { CUSTOMER_DETAIL_FIELDS_KEY } from '@/enums/cacheEnum';
  import { getClueDetail, getPublicClueDetail } from '@/api/detail';
  import { useFieldControl } from '@/components/FieldControl/useFieldControl';
  import type { CustomerDetail } from '@/api/detail';
  import type { FieldGroup } from '@/components/FieldControl/types';
  import { submitFormDialog } from '@/views/client/myClients/CallForm';
  import emitter from '@/utils/eventBus';
  import { orderStatusEnum, orderNodeEnum, submissionCapital } from '@/enums/detailEnum';
  import ClientInfoForm from './components/ClientInfoForm/index.vue';
  import { useDialog } from 'naive-ui';

  const props = withDefaults(
    defineProps<{
      clueType?: 'publicClue' | 'clue';
    }>(),
    {
      clueType: 'clue',
    }
  );
  const dialog = useDialog();
  type customsValueType = 'YISHUN' | 'DEYI';
  const emit = defineEmits(['close']);
  //资方枚举
  const submissionCapitalOptions = [
    { label: '易顺', value: 'YISHUN' },
    { label: '德易', value: 'DEYI' },
  ];
  const visible = ref(false);
  const clueId = ref(0);
  const showFieldControlModal = ref(false);
  const showMarkStatusModal = ref(false);
  const addFollowUpDrawerRef =
    useTemplateRef<InstanceType<typeof AddFollowUpDrawer>>('addFollowUpDrawerRef');
  const updateCustomerModalRef =
    useTemplateRef<InstanceType<typeof UpdateCustomerModal>>('updateCustomerModalRef');

  const localDetail = ref<CustomerDetail | null>(null);
  const isLoading = ref(false);
  const tabValue = ref('followUp');
  const customsValue = ref<customsValueType>('YISHUN');
  // --- Field Management ---
  const initialFieldConfigs: FieldGroup[] = [
    {
      title: '客户基本信息',
      configs: [
        { key: 'clueInfoVo.followStatus', label: '跟进状态', defaultVisible: true },
        { key: 'clueInfoVo.mobileNo', label: '电话号码', defaultVisible: true },
        { key: 'clueInfoVo.idCardNumber', label: '身份证号', defaultVisible: true },
        { key: 'clueInfoVo.sex', label: '性别', defaultVisible: true },
        // { key: 'clueInfoVo.licensePlateNumber', label: '车牌号', defaultVisible: true },
        { key: 'clueInfoVo.location', label: '所在地区', defaultVisible: true },
        {
          key: 'loanApplicationMaterials.personalInfo.email',
          label: '电子邮箱',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.educationLevel',
          label: '学历',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.maritalStatus',
          label: '婚姻状况',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.childCount',
          label: '子女数量',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.residenceAddress',
          label: '居住地址',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.houseType',
          label: '房产类型',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.company',
          label: '工作单位',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.companyAddress',
          label: '单位地址',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.companyPhone',
          label: '单位电话',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.companyNature',
          label: '单位性质',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.monthlyIncome',
          label: '月收入',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.contactName',
          label: '联系人姓名',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.contactPhone',
          label: '联系人号码',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.contactRelation',
          label: '联系人关系',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.personalInfo.threeElementsStatus',
          label: '三要素校验状态',
          defaultVisible: true,
        },
      ],
    },
    {
      title: '车辆信息',
      configs: [
        {
          key: 'loanApplicationMaterials.vehicleInfo.carBrand',
          label: '车辆品牌',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.bodyColor',
          label: '车身颜色',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.interiorColor',
          label: '内饰颜色',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.licenseCityName',
          label: '上牌城市',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.factoryDate',
          label: '出厂日期',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.transferCount',
          label: '过户次数',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.vehicleStatus',
          label: '车辆状态',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.carOfPersonStatus',
          label: '人车校验状态',
          defaultVisible: true,
        },
        {
          key: 'clueInfoVo.licensePlateNumber',
          label: '车牌号',
          defaultVisible: true,
        },
        {
          key: 'loanApplicationMaterials.vehicleInfo.carValuation',
          label: '车辆估值',
          defaultVisible: true,
        },
      ],
    },
  ];

  const { visibleFieldsByGroup } = useFieldControl(CUSTOMER_DETAIL_FIELDS_KEY, initialFieldConfigs);

  /**
   * @description: 判断是否禁用补充资料按钮
   * 三个条件需要同时满足：
   * 1. 有订单记录
   * 2. 是德易订单
   * 3. 需要显示"订单跟进"按钮
   */
  const isUpdateCustomerDisabled = computed(() => {
    const loanOrderRecords = localDetail.value?.loanOrderRecords;
    console.log('loanOrderRecords=====', loanOrderRecords);
    // 条件1：必须有订单记录
    if (!loanOrderRecords || loanOrderRecords.length === 0) {
      return false;
    }

    // 条件2和3：检查是否存在德易订单且需要显示"订单跟进"按钮
    return loanOrderRecords.some((row) => {
      // 条件2：判断是否是德易
      const isDeYi = String(row.managementCode) === String(submissionCapital.DE_SHUN);
      if (!isDeYi) {
        return false;
      }

      // 条件3：判断是否需要显示"订单跟进"按钮
      // 不显示按钮的条件：status === REJECT 或 (status === PASS && orderNode === LOAN)
      // 所以显示按钮的条件是：status !== REJECT && !(status === PASS && orderNode === LOAN)
      return (
        row.status !== orderStatusEnum.REJECT &&
        !(row.status === orderStatusEnum.PASS && row.orderNode === orderNodeEnum.LOAN)
      );
    });
  });

  /**
   * @description: 获取详情
   */
  async function fetchData(showLoading = true) {
    if (!clueId.value) return;
    if (showLoading) {
      isLoading.value = true;
    }
    try {
      const api = props.clueType === 'publicClue' ? getPublicClueDetail : getClueDetail;
      const { data } = await api(clueId.value);
      localDetail.value = data;
      console.log('localDetail', localDetail.value);
    } catch (error) {
      console.error('获取客户详情失败:', error);
      localDetail.value = null;
    } finally {
      isLoading.value = false;
    }
  }

  watch(
    () => visible.value,
    (show) => {
      if (show) {
        fetchData();
        emitter.on('workCallFinish', fetchData);
        emitter.on('reloadDetailData', fetchData);
      } else {
        localDetail.value = null; // 关闭时清空数据
        emitter.off('workCallFinish');
        emitter.off('reloadDetailData');
        tabValue.value = 'followUp';
      }
    },
    { immediate: true }
  );

  /**
   * @description: 接收子组件事件，刷新详情接口
   */
  async function handleRefresh() {
    await fetchData(false);
  }

  /**
   * @description: 打开抽屉
   */
  function openDrawer(id: number, tab?: string) {
    clueId.value = id;
    visible.value = true;
    if (tab) {
      tabValue.value = tab;
    }
  }

  function closeDrawer() {
    emit('close');
  }

  /**
   * @description: 标记状态
   */
  function handleMarkStatus() {
    // 如果已经标记为无效，不允许再次标记为无效
    if ((localDetail.value?.clueInfoVo as any)?.clueType === 0) {
      window.$message.error('线索类型已变更为无效，不可再次变更');
      return;
    }

    // 如果没有跟进记录，提示'请添加跟进记录后重试'
    if (
      !localDetail.value?.clueFollowUpRecords ||
      localDetail.value?.clueFollowUpRecords.length === 0
    ) {
      window.$message.error('请添加跟进记录后重试');
      return;
    }

    showMarkStatusModal.value = true;
  }
  /**
   * tab切换时  切换到签约记录需要做是否加微信前置校验& 客户资料 前置校验
   *
   */
  function handleTabBeforeChange(newTab: string) {
    if (newTab === 'loanRecord') {
      if (localDetail.value?.clueInfoVo?.addWeChat !== 1) {
        dialog.warning({
          title: '无法创建贷款订单',
          content: '当前客户未添加微信，请确认跟进情况是否加微以及加微类型状态',
          positiveText: '去跟进',
          negativeText: '取消',
          onPositiveClick: () => {
            addFollowUpDrawerRef.value?.openDrawer(clueId.value);
          },
        });
        return false;
      }
    }
    console.log('isUpdateCustomerDisabled', isUpdateCustomerDisabled.value);

    if (newTab === 'customerInfo') {
      return !isUpdateCustomerDisabled.value;
    }
    return true;
  }
  defineExpose({
    openDrawer,
  });
</script>

<style lang="less" scoped>
  @import '@/styles/custom/scrollbar.less';

  .n-avatar {
    background-color: #fff;
  }
  :deep(.n-drawer-header__main) {
    width: 100%;
  }
  :deep(.n-drawer-body-content-wrapper) {
    height: 100%;
    padding: 10px !important;
    background-color: #eee;
  }

  .n-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
    :deep(.n-tabs-pane-wrapper) {
      overflow-y: auto;
      padding-right: 10px;
      .custom-scrollbar();
    }
  }
  :deep(.n-tab-pane) {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .n-data-table {
    padding-bottom: 20px;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .header-info {
    display: flex;
    align-items: center;

    &-text {
      margin-left: 12px;
      .name {
        font-size: 16px;
        font-weight: bold;
      }
      .time {
        font-size: 12px;
        color: #999;
        margin-top: 10px;
      }
    }
  }

  .drawer-body {
    display: flex;
    height: 100%;
    gap: 16px;

    &-left,
    &-right {
      background-color: #fff;
      border-radius: 4px;
    }

    &-left {
      width: 300px;
      max-height: 100%;
      display: flex;
      flex-direction: column;

      .left-content {
        max-height: 100%;
        overflow-y: auto;
        padding: 16px 12px 16px 10px;
        margin-right: 4px;
        .custom-scrollbar();
      }

      .left-footer {
        padding: 16px;
        border-top: 1px solid #eee;
      }
    }

    &-right {
      flex: 1;
      padding: 16px 4px 16px 16px;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      min-width: 0;

      .right-content {
        flex: 1;
        min-height: 0;
      }

      .right-footer {
        padding-top: 16px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: center;
        gap: 50px;
      }
    }
  }
  .client-info-form-p {
    border: 1px solid #eee;
    padding: 0 5px;
  }
</style>
