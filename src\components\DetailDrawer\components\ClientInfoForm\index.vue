<script setup lang="tsx">
  import YishunForm from './components/YishunForm.vue';
  import DeyiForm from './components/DeyiForm.vue';
  import { computed, ref } from 'vue';
  import { customerMaterialSupplement } from '@/api/detail';
  import type { CustomerDetail } from '@/api/detail';

  import { MATCHTYPE } from '@/components/DetailDrawer/components/ClientInfoForm/types/yishunForm';
  const props = defineProps<{
    showForm: 'YISHUN' | 'DEYI';
    clueId: number | string;
    localDetail: CustomerDetail | null;
  }>();
  const yishunRef = ref();
  const deyiRef = ref();
  const formData = computed(() => {
    if (props.showForm === 'YISHUN') {
      return yishunRef?.value?.linkageFormData;
    } else if (props.showForm === 'DEYI') {
      return deyiRef.value?.linkageFormData;
    } else {
      return {};
    }
  });
  //资方类型：1, "自有CRM"), EASY(3, "易顺CRM"),DY(4, "德易"),LIAN_ZHONG(5, "联众")
  function handleSave() {
    if (!MATCHTYPE[props.showForm]) {
      return;
    }
    customerMaterialSupplement({
      clueId: props.clueId,
      matchType: MATCHTYPE[props.showForm],
      fieldData: formData.value,
      ...(props.showForm === 'YISHUN' ? { ysCustomerDataSaveStatus: '2' } : {}),
      ...(props.showForm === 'DEYI' ? { dyCustomerDataSaveStatus: '2' } : {}),
    });
  }
  function getWechatSiteUrl() {
    return `${import.meta.env.VITE_WECHAT_SITE_URL}/information${
      props.showForm === 'YISHUN' ? 'yishun' : 'deyi'
    }?mobileMd5=${props.localDetail?.clueInfoVo?.mobile}`;
  }
</script>
<template>
  <div>
    <div class="client-info-form">
      <YishunForm :localDetail="localDetail || {}" ref="yishunRef" v-if="showForm === 'YISHUN'" />
      <DeyiForm :localDetail="localDetail || {}" ref="deyiRef" v-if="showForm === 'DEYI'" />
    </div>
    <div class="flex items-center w-full justify-center p-[5px] button-container">
      <n-button type="primary" class="mr-5" @click="handleSave">保存</n-button>
      <n-button type="primary">生成留资二维码</n-button>
    </div>
  </div>

  <!-- <DynamicForm
      ref="linkageFormRef"
      v-model:config="linkageFormConfig as any"
      v-model="linkageFormData"
    /> -->
</template>
<style lang="less" scoped>
  //   @import '@/styles/custom/scrollbar.less';
  .client-info-form {
    max-height: 58vh;
    overflow-y: auto;
    // .custom-scrollbar();
  }
  .button-container {
    border-top: 1px solid #eee;
  }
</style>
