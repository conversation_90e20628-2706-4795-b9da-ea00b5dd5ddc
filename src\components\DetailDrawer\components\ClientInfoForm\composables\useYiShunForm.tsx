import { FiledOptions, validators } from '@/components/Form';
import type {
  DynamicFormInstance,
  DynamicFormConfig,
} from '@/components/Form/src/types/dynamicForm';
import { ref, computed, ComputedRef, onMounted } from 'vue';
import { cloneDeep } from 'lodash-es';
import CitySelect from '@/components/CitySelect/index.vue';
import { validPhone, validIdCard } from '@/utils/formValidationRules';
import {
  type IUpdateCustomerModalFormModel,
  contactRelationOptions,
  contactRelationOptions1,
} from '@/components/DetailDrawer/components/ClientInfoForm/types/yishunForm';
import { getFieldConfig, getFieldData } from '@/api/dashboard/deyi';
import UploadFile from '@/components/UploadFile/index.vue';
import { imageToJson, jsonToImage, validateCityCode } from '@/components/DrawerOrder/utils';
import { getUserInfoQuery } from '@/api/detail';

export function useYiShunForm({ mobile }) {
  // 表单引用
  const linkageFormRef = ref<DynamicFormInstance>();
  const citySelectRef = ref();
  const DEFAULT_FORM_MODEL: IUpdateCustomerModalFormModel = {
    clueId: null,
    vehicleColor: '',
    interiorColor: '',
    licenseCity: '',
    manufactureDate: null,
    psvaTransferTimes: '',
    preAuditVehicleStatus: null,
    psvaPlateNumber: '',
    preAuditName: '',
    psvaCityCode: null,
    cityName: '',
    provinceName: '',
    provinceCode: null,
    ipEduLevel: null,
    ipMaritalStatus: null,
    ipSpouseName: '', //配偶姓名
    ipSpousePhone: '', //配偶手机号
    ipSpouseId: '', //配偶身份证号
    spouseCompanyName: '', //配偶工作单位
    spouseWorkAddress: '', //配偶工作单位地址
    spouseWorkUnitDetailAddress: '', //配偶工作单位详细地址
    childrenCount: null,
    ipResDetail: '',
    propertyType: null,
    ipEmployerName: '',
    ipWorkDetail: '',
    companyPhone: null,
    ipEmployerType: null,
    ipNetMonthlyIncome: null,
    ipIndustry: null, //从事行业种类
    profession: null, //职业
    //联系人关系
    contactRelation: [
      { relationshipName: '', name: '', mobile: '' },
      { relationshipName: '', name: '', mobile: '' },
      { relationshipName: '', name: '', mobile: '' },
    ],
    // contactRelation1: null,
    // contactName1: '',
    // contactPhone1: null,
    // contactRelation2: null,
    // contactName2: '',
    // contactPhone2: null,
    // contactRelation3: null,
    // contactName3: '',
    // contactPhone3: null,
    preAuditIdCardNum: null,
    preAuditIdCardReverse: '',
    preAuditIdCardFront: '',
    ipAttachVehicleLicense: '',
    ysVehicleLicenseBack: '',
    ipAttachDrivingLicense: '',
    driverLicenseBack: '',
    ysBankCardFront: '',
    ysBankCardBack: '',
    funderCardFrontLeft45: null,
    funderCardRearRight45: null,
    //人车合影
    funderCardVehiclePhoto: null,
    //车头全景
    ysCarFrontPanorama: null,
    //车辆登记证
    psvaCarRegisCert: null,
    //交管12123录屏
    traffic12123Medias: null,
    //交强险
    compulsoryInsurance: null,
    //商业险
    gimaCommercialPolicy: null,
  };
  // 表单数据
  const linkageFormData = ref<any>(cloneDeep(DEFAULT_FORM_MODEL));
  // 选项配置

  // 联系人关系选项，根据婚姻状况联动
  // const _contactRelationOptions = computed(() => {
  //   const ipMaritalStatus = linkageFormData.value.personalInfo?.ipMaritalStatus;

  //   if (ipMaritalStatus === 1) {
  //     return [contactRelationOptions[0]]; // 未婚只能选择父/母
  //   }

  //   if (ipMaritalStatus === 2) {
  //     return [contactRelationOptions[1]]; // 已婚只能选择配偶
  //   }

  //   return [];
  // });

  // 联动表单配置
  const linkageFormConfig: ComputedRef<DynamicFormConfig> = computed(() => ({
    labelWidth: 120,
    columns: 24,
    showActionButtons: false,
    labelPlacement: 'left',
    fields: [
      // 车辆信息部分
      {
        field: 'vehicleInfo',
        label: '车辆信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'vehicleColor',
        label: '车身颜色',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入车身颜色',
      },
      {
        field: 'interiorColor',
        label: '内饰颜色',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入内饰颜色',
      },
      {
        field: 'licenseCity',
        label: '上牌城市',
        type: FiledOptions.CUSTOM,
        span: 8,
        render: (props, ctx) => {
          return (
            <CitySelect
              value={props.value}
              change-on-select
              multiple={false}
              ref={citySelectRef}
              onUpdate:value={(value) => {
                ctx.emit('update:value', value);
              }}
            />
          );
        },
      },
      {
        field: 'manufactureDate',
        label: '出厂日期',
        type: FiledOptions.DATE,
        span: 8,
        componentProps: {
          type: 'date',
          clearable: true,
          format: 'yyyy-MM-dd',
        },
      },
      {
        field: 'preAuditVehicleStatus',
        label: '车辆状态',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.preAuditVehicleStatus,
        placeholder: '请选择车辆状态',
      },
      {
        field: 'psvaTransferTimes',
        label: '过户次数',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.psvaTransferTimes,
        placeholder: '请选择过户次数',
      },
      {
        field: 'psvaPlateNumber',
        label: '车牌号',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入车牌号',
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value && value?.length < 7) {
                callback(new Error('请输入正确的车牌号'));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      // 个人信息部分
      {
        field: 'personalInfo',
        label: '申请人信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'preAuditName',
        label: '姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入姓名',
      },
      {
        field: 'preAuditIdCardNum',
        label: '身份证号',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入身份证号',
        componentProps: {
          maxlength: 18,
        },
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validIdCard(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      {
        field: 'psvaCityCode',
        label: '所在城市',
        type: FiledOptions.CUSTOM,
        span: 8,
        render: (props, ctx) => {
          return (
            <CitySelect
              value={props.value}
              change-on-select
              multiple={false}
              onUpdate:value={(value) => {
                ctx.emit('update:value', value);
              }}
            />
          );
        },
      },
      {
        field: 'ipEduLevel',
        label: '申请人学历情况',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.ipEduLevel,
        placeholder: '请选择申请人学历情况',
      },
      {
        field: 'ipMaritalStatus',
        label: '婚姻状况',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.ipMaritalStatus,
        placeholder: '请选择婚姻状况',
      },
      {
        field: 'ipSpouseName',
        label: '配偶姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入配偶姓名',
      },
      {
        field: 'ipSpousePhone',
        label: '配偶手机号',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入配偶手机号',
        componentProps: {
          maxlength: 11,
        },
      },
      {
        field: 'ipSpouseId',
        label: '配偶身份证号',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入配偶身份证号',
        componentProps: {
          maxlength: 18,
        },
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validIdCard(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      {
        field: 'spouseCompanyName',
        label: '配偶工作单位名称',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入配偶工作单位名称',
      },
      {
        field: 'spouseWorkAddress',
        label: '配偶工作单位地址',
        type: FiledOptions.CUSTOM,
        span: 8,
        render: (props, ctx) => {
          return (
            <CitySelect
              value={props.value}
              change-on-select
              multiple={false}
              onUpdate:value={(value) => {
                ctx.emit('update:value', value);
              }}
            />
          );
        },
      },
      {
        field: 'spouseWorkUnitDetailAddress',
        label: '工作单位详细地址',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入工作单位详细地址',
      },
      {
        field: 'propertyType',
        label: '房产类型',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.propertyType,
        placeholder: '请选择房产类型',
      },
      {
        field: 'childrenCount',
        label: '子女数量',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.childrenCount,
        placeholder: '请选择子女数量',
      },
      {
        field: 'ipResDetail',
        label: '居住详细地址',
        type: FiledOptions.INPUT,
        span: 16,
        placeholder: '请输入居住详细地址',
      },
      //工作信息
      {
        field: 'workInfo',
        label: '工作信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'ipEmployerName',
        label: '申请人单位名称',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入申请人单位名称',
      },
      {
        field: 'ipWorkDetail',
        label: '申请人单位详细地址',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入申请人单位详细地址',
      },
      {
        field: 'companyPhone',
        label: '单位电话',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入单位电话',
        rules: [validators.phone('请输入正确的单位电话')],
        componentProps: {
          maxlength: 11,
        },
      },
      {
        field: 'ipEmployerType',
        label: '申请人单位性质',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.ipEmployerType,
        placeholder: '请选择申请人单位性质',
      },
      {
        field: 'ipIndustry',
        label: '从事行业种类',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.ipIndustry,
        placeholder: '请选择从事行业种类',
      },
      {
        field: 'profession',
        label: '职业',
        type: FiledOptions.SELECT,
        span: 8,
        options: selectOptions.value.profession,
        placeholder: '请选择职业',
      },
      {
        field: 'ipNetMonthlyIncome',
        label: '税后月收入',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请选择税后月收入',
      },
      // 联系人信息
      {
        field: 'contactInfo',
        label: '联系人信息',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'contactRelation.0.relationshipName',
        label: '联系人关系1',
        tooltip: '直系亲属',
        type: FiledOptions.SELECT,
        span: 8,
        options: contactRelationOptions,
        componentProps: {
          labelField: 'label',
          valueField: 'label',
        },
        placeholder: '请选择联系人关系1',
      },
      {
        field: 'contactRelation.0.name',
        label: '联系人1姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人1姓名',
      },
      {
        field: 'contactRelation.0.mobile',
        label: '联系人1号码',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人1号码',
        componentProps: {
          maxlength: 11,
        },
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validPhone(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      {
        field: 'contactRelation.1.relationshipName',
        label: '联系人关系2',
        type: FiledOptions.SELECT,
        span: 8,
        options: contactRelationOptions,
        componentProps: {
          labelField: 'label',
          valueField: 'label',
        },
        placeholder: '请选择联系人关系2',
        tooltip: '直系亲属',
      },
      {
        field: 'contactRelation.1.name',
        label: '联系人2姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人2姓名',
      },
      {
        field: 'contactRelation.1.mobile',
        label: '联系人2号码',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人2号码',
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validPhone(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      {
        field: 'contactRelation.2.relationshipName',
        label: '联系人关系3',
        type: FiledOptions.SELECT,
        span: 8,
        options: contactRelationOptions1,
        componentProps: {
          labelField: 'label',
          valueField: 'label',
        },
        placeholder: '请选择联系人关系3',
      },
      {
        field: 'contactRelation.2.name',
        label: '联系人3姓名',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人3姓名',
      },
      {
        field: 'contactRelation.2.mobile',
        label: '联系人3号码',
        type: FiledOptions.INPUT,
        span: 8,
        placeholder: '请输入联系人3号码',
        rules: [
          {
            validator: (_rule: any, value: any, callback: (error?: string | Error) => void) => {
              if (value) {
                const result = validPhone(_rule, value);
                if (result instanceof Error) {
                  callback(result);
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: ['blur', 'input', 'change'],
          },
        ],
      },
      // 证照信息部分
      {
        field: 'certificateInfo',
        label: '附件资料',
        type: FiledOptions.DIVIDER,
        span: 24,
      },
      {
        field: 'preAuditIdCardReverse',
        label: '身份证正面(国徽面)',
        labelPlacement: 'top',
        labelAlign: 'left',
        type: FiledOptions.CUSTOM,
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'preAuditIdCardFront',
        label: '身份证背面',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'ipAttachVehicleLicense',
        label: '行驶证正面',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'ysVehicleLicenseBack',
        label: '行驶证背面',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'ipAttachDrivingLicense',
        label: '驾驶证正面',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'driverLicenseBack',
        label: '驾驶证背面',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'ysBankCardFront',
        label: '银行卡正面',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'ysBankCardBack',
        label: '银行卡背面',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },

      {
        field: 'funderCardFrontLeft45',
        label: '车左前45°',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'funderCardRearRight45',
        label: '车身右后方45°',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'funderCardVehiclePhoto',
        label: '人车合影',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'ysCarFrontPanorama',
        label: '车头全景',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'psvaCarRegisCert',
        label: '车辆登记证',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'traffic12123Medias',
        label: '交管12123录屏',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'compulsoryInsurance',
        label: '交强险',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
      {
        field: 'gimaCommercialPolicy',
        label: '商业险',
        type: FiledOptions.CUSTOM,
        labelPlacement: 'top',
        labelAlign: 'left',
        span: 4,
        render: (props, ctx) => {
          return (
            <UploadFile
              fileList={jsonToImage(props.value || '')}
              accept=".jpg,.jpeg,.png,.pdf"
              max-size={10}
              max-count={1}
              force-array
              onUpdate:fileList={(val) => ctx.emit('update:value', imageToJson(val))}
            />
          );
        },
      },
    ],
  }));
  const selectOptions = ref({
    preAuditVehicleStatus: [],
    psvaTransferTimes: [],
    ipEduLevel: [],
    ipMaritalStatus: [],
    propertyType: [],
    childrenCount: [],
    ipEmployerType: [],
    ipIndustry: [],
    profession: [],
  });
  function getOptions() {
    getFieldConfig({
      fieldName: [
        'preAuditVehicleStatus',
        'psvaTransferTimes',
        'ipEduLevel',
        'ipMaritalStatus',
        'propertyType',
        'childrenCount',
        'ipEmployerType',
        'ipIndustry',
        'profession',
      ],
    }).then((res) => {
      if (res.data) {
        res.data?.forEach((item) => {
          if (item.ysFieldOptions && item.fieldName in selectOptions.value) {
            selectOptions.value[item.fieldName] = JSON.parse(item.ysFieldOptions || '[]').map(
              (item) => ({
                label: item,
                value: item,
              })
            );
          }
        });
      }
    });
  }
  function getFileValue() {
    getUserInfoQuery({
      matchType: 3,
      mobile,
      fieldName: Object.keys(DEFAULT_FORM_MODEL),
    }).then((res) => {
      if (res.data) {
        linkageFormData.value = res.data;
      }
    });
  }
  onMounted(() => {
    getFileValue();
    getOptions();
  });
  return {
    linkageFormRef,
    linkageFormData,
    linkageFormConfig,
  };
}
