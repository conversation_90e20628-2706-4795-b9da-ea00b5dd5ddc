<script setup lang="tsx">
  import { DynamicForm } from '@/components/Form';
  import { useYiShunForm } from '@/components/DetailDrawer/components/ClientInfoForm/composables/useYiShunForm';
  const props = defineProps({
    localDetail: {
      type: Object,
      default: () => ({}),
    },
  });
  const { linkageFormRef, linkageFormData, linkageFormConfig } = useYiShunForm({
    mobile: props.localDetail?.clueInfoVo?.mobileNo,
  });

  defineExpose({
    linkageFormData,
  });
</script>
<template>
  <DynamicForm ref="linkageFormRef" v-model="linkageFormData" v-model:config="linkageFormConfig" />
</template>
<style lang="less" scoped></style>
